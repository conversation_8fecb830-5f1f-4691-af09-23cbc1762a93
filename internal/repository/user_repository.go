// Package repository provides data access layer implementations.
package repository

import (
	"context"
	"errors"

	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5/pgxpool"

	"telescope-be/internal/entity"
)

// UserRepository defines the interface for user-related database operations
type UserRepository interface {
	Register(ctx context.Context, user entity.User) error
	CheckUserExistByEmail(ctx context.Context, email string) (bool, error)
	GetUserByEmail(ctx context.Context, email string) (*entity.User, error)
	GetUserByID(ctx context.Context, userID int) (*entity.User, error)
	UpdatePassword(ctx context.Context, user entity.User) error
}

// userRepository implements the UserRepository interface
type userRepository struct {
	pool *pgxpool.Pool
}

// NewUserRepository creates a new user repository instance
func NewUserRepository(pool *pgxpool.Pool) UserRepository {
	return &userRepository{
		pool: pool,
	}
}

// Register a new user in the database
func (r *userRepository) Register(ctx context.Context, user entity.User) error {
	query := `
		INSERT INTO users (
			external_id, first_name, last_name, username, email, password_hash,
			phone_number, status, country_id, created_at, updated_at
		)
        	VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id
	`

	err := r.pool.QueryRow(ctx, query,
		user.ExternalID,
		user.FirstName,
		user.LastName,
		user.Username,
		user.Email,
		user.PasswordHash,
		user.PhoneNumber,
		user.Status,
		user.CountryID,
		user.CreatedAt,
		user.UpdatedAt,
	).Scan(&user.ID)

	return err
}

func (r *userRepository) CheckUserExistByEmail(ctx context.Context, email string) (bool, error) {
	query := `
		SELECT EXISTS (SELECT 1 FROM users WHERE email = $1)
	`

	var exists bool
	err := r.pool.QueryRow(ctx, query, email).Scan(&exists)
	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *userRepository) GetUserByEmail(ctx context.Context, email string) (*entity.User, error) {
	query := `
		SELECT
			id, external_id, first_name, last_name, username, email, password_hash,
			phone_number, status, country_id, created_at, updated_at, deleted_at
		FROM users
		WHERE email = $1 AND deleted_at IS NULL
	`

	var user entity.User
	err := pgxscan.Get(ctx, r.pool, &user, query, email)
	if err != nil {
		if pgxscan.NotFound(err) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	return &user, nil
}

func (r *userRepository) GetUserByID(ctx context.Context, userID int) (*entity.User, error) {
	query := `
		SELECT
			id, external_id, first_name, last_name, username, email, password_hash,
			phone_number, status, country_id, created_at, updated_at, deleted_at
		FROM users
		WHERE id = $1 AND deleted_at IS NULL
	`

	var user entity.User
	err := pgxscan.Get(ctx, r.pool, &user, query, userID)
	if err != nil {
		if pgxscan.NotFound(err) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	return &user, nil
}

// UpdatePassword used to update user password
func (r *userRepository) UpdatePassword(ctx context.Context, user entity.User) error {
	query := `
		UPDATE users SET password_hash = $1, updated_at = $2 WHERE id = $3`

	_, err := r.pool.Exec(ctx, query, user.PasswordHash, user.UpdatedAt, user.ID)
	if err != nil {
		if pgxscan.NotFound(err) {
			return errors.New("user not found")
		}
		return err
	}

	return nil
}
