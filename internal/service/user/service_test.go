package user

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"telescope-be/internal/entity"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

// Mock implementations for testing
type mockUserRepository struct {
	shouldExist   bool
	existsError   error
	registerError error
}

func (m *mockUserRepository) Register(ctx context.Context, user entity.User) error {
	if m.registerError != nil {
		return m.registerError
	}
	// TODO: In real implementation, ID would be set via database RETURNING clause
	// but since user is passed by value, the modification doesn't propagate back
	// This is consistent with the real repository implementation
	return nil
}

func (m *mockUserRepository) CheckUserExistByEmail(ctx context.Context, email string) (bool, error) {
	if m.existsError != nil {
		return false, m.existsError
	}
	return m.shouldExist, nil
}

func (m *mockUserRepository) GetUserByEmail(ctx context.Context, email string) (*entity.User, error) {
	return nil, nil // Not implemented for this test
}

func (m *mockUserRepository) GetUserByID(ctx context.Context, userID int) (*entity.User, error) {
	return nil, nil // Not implemented for this test
}

func (m *mockUserRepository) UpdatePassword(ctx context.Context, user entity.User) error {
	return nil // Not implemented for this test
}

func TestUserService_RegisterUser(t *testing.T) {
	logger := zaptest.NewLogger(t)

	tests := []struct {
		name             string
		request          presentation.UserRequest
		mockSetup        func() *repository.Repository
		expectedError    string
		validateResponse func(t *testing.T, resp *presentation.UserRegisterResponse)
	}{
		{
			name: "successful user registration",
			request: presentation.UserRequest{
				Email:     "<EMAIL>",
				Username:  "testuser",
				Password:  "password123",
				CountryID: 1,
			},
			mockSetup: func() *repository.Repository {
				return &repository.Repository{
					User: &mockUserRepository{
						shouldExist: false,
					},
				}
			},
			expectedError: "",
			validateResponse: func(t *testing.T, resp *presentation.UserRegisterResponse) {
				assert.Equal(t, "<EMAIL>", resp.Email)
				assert.Equal(t, "testuser", resp.Username)
				assert.Equal(t, 1, resp.CountryID)

				// ID should be a valid UUIDv7 string
				assert.NotEmpty(t, resp.ID)
				parsedUUID, err := uuid.Parse(resp.ID)
				assert.NoError(t, err, "ID should be a valid UUID")
				assert.Equal(t, uuid.Version(7), parsedUUID.Version(), "ID should be UUIDv7")

				assert.False(t, resp.CreatedAt.IsZero())
				assert.False(t, resp.UpdatedAt.IsZero())
			},
		},
		{
			name: "user already exists",
			request: presentation.UserRequest{
				Email:     "<EMAIL>",
				Username:  "existinguser",
				Password:  "password123",
				CountryID: 1,
			},
			mockSetup: func() *repository.Repository {
				return &repository.Repository{
					User: &mockUserRepository{
						shouldExist: true,
					},
				}
			},
			expectedError: "account already exists",
		},
		{
			name: "database error during existence check",
			request: presentation.UserRequest{
				Email:     "<EMAIL>",
				Username:  "erroruser",
				Password:  "password123",
				CountryID: 1,
			},
			mockSetup: func() *repository.Repository {
				return &repository.Repository{
					User: &mockUserRepository{
						existsError: errors.New("database connection error"),
					},
				}
			},
			expectedError: "database connection error",
		},
		{
			name: "registration failure",
			request: presentation.UserRequest{
				Email:     "<EMAIL>",
				Username:  "registeruser",
				Password:  "password123",
				CountryID: 1,
			},
			mockSetup: func() *repository.Repository {
				return &repository.Repository{
					User: &mockUserRepository{
						shouldExist:   false,
						registerError: errors.New("failed to insert user"),
					},
				}
			},
			expectedError: "failed to insert user",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := tt.mockSetup()
			service := NewUserService(mockRepo, logger)

			// Execute
			result, err := service.RegisterUser(context.Background(), tt.request)

			// Verify
			if tt.expectedError != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)
				if tt.validateResponse != nil {
					tt.validateResponse(t, result)
				}
			}
		})
	}
}

func TestUserService_RegisterUser_UUIDv7Generation(t *testing.T) {
	logger := zaptest.NewLogger(t)

	mockRepo := &repository.Repository{
		User: &mockUserRepository{
			shouldExist: false,
		},
	}

	service := NewUserService(mockRepo, logger)

	request := presentation.UserRequest{
		Email:     "<EMAIL>",
		Username:  "uuiduser",
		Password:  "password123",
		CountryID: 1,
	}

	result, err := service.RegisterUser(context.Background(), request)

	require.NoError(t, err)
	require.NotNil(t, result)

	// Parse the UUID and validate it's UUIDv7
	parsedUUID, err := uuid.Parse(result.ID)
	require.NoError(t, err, "Should generate a valid UUID")
	assert.Equal(t, uuid.Version(7), parsedUUID.Version(), "Should generate UUIDv7")

	// UUIDv7 should have proper time ordering - test multiple generations
	var previousUUID uuid.UUID
	for i := 0; i < 5; i++ {
		result2, err2 := service.RegisterUser(context.Background(), request)
		require.NoError(t, err2)

		currentUUID, err3 := uuid.Parse(result2.ID)
		require.NoError(t, err3)
		assert.Equal(t, uuid.Version(7), currentUUID.Version())

		// UUIDv7 should be monotonically increasing (time-ordered)
		if i > 0 {
			assert.True(t, strings.Compare(currentUUID.String(), previousUUID.String()) > 0,
				"UUIDv7 should be time-ordered")
		}
		previousUUID = currentUUID
	}
}

func TestUserService_RegisterUser_PasswordHashing(t *testing.T) {
	logger := zaptest.NewLogger(t)

	mockRepo := &repository.Repository{
		User: &mockUserRepository{
			shouldExist: false,
		},
	}

	service := NewUserService(mockRepo, logger)

	request := presentation.UserRequest{
		Email:     "<EMAIL>",
		Username:  "testuser",
		Password:  "plaintext123",
		CountryID: 1,
	}

	result, err := service.RegisterUser(context.Background(), request)

	require.NoError(t, err)
	require.NotNil(t, result)

	// Verify basic fields
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, "testuser", result.Username)

	// Verify that password is not stored in plain text (this is implicit in our logic)
	// The actual password hashing happens in the service, we can't directly test the hash
	// without accessing the entity, but we can verify the method doesn't fail
}

func TestUserService_RegisterUser_EmptyFields(t *testing.T) {
	logger := zaptest.NewLogger(t)

	mockRepo := &repository.Repository{
		User: &mockUserRepository{
			shouldExist: false,
		},
	}

	service := NewUserService(mockRepo, logger)

	// Test with empty email
	request := presentation.UserRequest{
		Email:     "",
		Username:  "testuser",
		Password:  "password123",
		CountryID: 1,
	}

	result, err := service.RegisterUser(context.Background(), request)

	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, "", result.Email)
}

func TestUserService_RegisterUser_LongInputs(t *testing.T) {
	logger := zaptest.NewLogger(t)

	mockRepo := &repository.Repository{
		User: &mockUserRepository{
			shouldExist: false,
		},
	}

	service := NewUserService(mockRepo, logger)

	longEmail := strings.Repeat("a", 100) + "@example.com"
	longUsername := strings.Repeat("b", 100)

	request := presentation.UserRequest{
		Email:     longEmail,
		Username:  longUsername,
		Password:  "password123",
		CountryID: 1,
	}

	result, err := service.RegisterUser(context.Background(), request)

	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, longEmail, result.Email)
	assert.Equal(t, longUsername, result.Username)
}

// Benchmark test for performance measurement
func BenchmarkUserService_RegisterUser(b *testing.B) {
	logger := zaptest.NewLogger(b)

	mockRepo := &repository.Repository{
		User: &mockUserRepository{
			shouldExist: false,
		},
	}

	service := NewUserService(mockRepo, logger)

	request := presentation.UserRequest{
		Email:     "<EMAIL>",
		Username:  "benchuser",
		Password:  "password123",
		CountryID: 1,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.RegisterUser(context.Background(), request)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Test context cancellation behavior
func TestUserService_RegisterUser_ContextCancellation(t *testing.T) {
	logger := zaptest.NewLogger(t)

	mockRepo := &repository.Repository{
		User: &mockUserRepository{
			existsError: context.Canceled,
		},
	}

	service := NewUserService(mockRepo, logger)

	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	request := presentation.UserRequest{
		Email:     "<EMAIL>",
		Username:  "contextuser",
		Password:  "password123",
		CountryID: 1,
	}

	result, err := service.RegisterUser(ctx, request)
	assert.Error(t, err)
	assert.Nil(t, result)
}
